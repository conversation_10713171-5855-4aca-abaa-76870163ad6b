import { component$ } from '@qwik.dev/core';

export type StockLevel = 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK';

export default component$<{ stockLevel?: string }>(({ stockLevel }) => {
	let stockLevelLabel = '';
	let badgeClasses = 'bg-gray-800 text-gray-300';
	switch (stockLevel as StockLevel) {
		case 'IN_STOCK':
			stockLevelLabel = $localize`In stock`;
			badgeClasses = 'bg-green-900 text-green-300';
			break;
		case 'OUT_OF_STOCK':
			stockLevelLabel = $localize`Out of stock`;
			badgeClasses = 'bg-red-900 text-red-300';
			break;
		case 'LOW_STOCK':
			stockLevelLabel = $localize`Low stock`;
			badgeClasses = 'bg-yellow-900 text-yellow-300';
			break;
	}
	return (
		<span
			class={'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ' + badgeClasses}
		>
			{stockLevelLabel}
		</span>
	);
});
