import { component$ } from '@qwik.dev/core';
import { Image } from 'qwik-image';
import { Collection } from '~/generated/graphql';

interface IProps {
	collection: Collection;
}

export default component$(({ collection }: IProps) => {
	return (
		<a href={`/collections/${collection.slug}`} key={collection.id} class="group">
			<div class="max-w-[300px] relative rounded-lg overflow-hidden xl:w-auto mx-auto transition-transform group-hover:scale-105">
				<div class="w-full h-full object-center object-cover">
					<Image
						layout="fixed"
						width="300"
						height="300"
						src={collection.featuredAsset?.preview}
						alt={`Image of: ${collection.name}`}
					/>
				</div>
				<span class="absolute w-full bottom-x-0 bottom-0 h-2/3 bg-gradient-to-t from-background/80 to-transparent" />
				<span class="absolute w-full bottom-2 mt-auto text-center text-xl font-bold text-white">
					{collection.name}
				</span>
			</div>
		</a>
	);
});
