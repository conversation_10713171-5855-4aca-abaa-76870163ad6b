import { $, component$, QRL } from '@qwik.dev/core';
import FilterIcon from '../icons/FilterIcon';

export default component$<{ onToggleMenu$: QRL<() => void> }>(({ onToggleMenu$ }) => {
	return (
		<button
			type="button"
			class="flex space-x-2 items-center border border-gray-600 rounded p-2 ml-4 sm:ml-6 text-gray-400 hover:text-white hover:border-gray-400 transition-colors lg:hidden"
			onClick$={$(async () => {
				onToggleMenu$();
			})}
		>
			<span class="text-gray-300 hover:text-white">Filters</span>
			<FilterIcon />
		</button>
	);
});
